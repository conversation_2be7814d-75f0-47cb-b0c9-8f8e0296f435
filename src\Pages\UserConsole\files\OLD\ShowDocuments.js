import React from 'react'
import { map } from "lodash" //https://lodash.com/docs/4.17.15#map

export default function ShowDocuments(props) {

    const {forms} = props
  
    return (
    <div className='documentCont'>
        
        {map(forms, item => {

            return(<DocumentLine key={item.FormID} item = {item}  />)

        })}

    </div>
  
    )
}

function DocumentLine(props) {

    const {item} = props

    return(
        <div className='document'>
            <div className="showStatus">
                <h3>{item.FormName}</h3>
                {item.Status === 'Missing' && <div>
                    <button>asdasd</button>
                </div>}
            </div>
        </div>
    );
    
}


