import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error("Application error:", error, errorInfo);

    try {
      localStorage.removeItem("userData");
      localStorage.removeItem("userInfo");
      localStorage.removeItem("sessionKey");
      localStorage.removeItem("rakazid");
      localStorage.removeItem("sayeretid");
    } catch (cleanupErr) {
      console.error("Error clearing user data:", cleanupErr);
    }

    const currentPath = window.location.pathname;
    if (!currentPath.startsWith("/login")) {
      window.location.replace("/login");
    }
  }

  render() {
    if (this.state.hasError) {
      return null;
    }

    return this.props.children;
  }
}

export default ErrorBoundary; 