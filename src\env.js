// Environment variables fallback
if (!process.env.REACT_APP_ENVIRONMENT) {
  process.env.REACT_APP_ENVIRONMENT = 'dev';
}

if (!process.env.REACT_APP_API_BASE_URL) {
  process.env.REACT_APP_API_BASE_URL = 'https://sherut-leumi.wdev.co.il/api/';
}

if (!process.env.REACT_APP_API_BASE_URL_DEV) {
  process.env.REACT_APP_API_BASE_URL_DEV = 'https://sherut-leumi-dev.wdev.co.il/api/';
}

// Debug mode for troubleshooting
if (!process.env.REACT_APP_DEBUG) {
  process.env.REACT_APP_DEBUG = 'true';
}

console.log('Environment variables loaded:', {
  REACT_APP_ENVIRONMENT: process.env.REACT_APP_ENVIRONMENT,
  REACT_APP_API_BASE_URL: process.env.REACT_APP_API_BASE_URL,
  REACT_APP_API_BASE_URL_DEV: process.env.REACT_APP_API_BASE_URL_DEV,
  REACT_APP_DEBUG: process.env.REACT_APP_DEBUG
});

// Global error handler for unhandled promise rejections
window.addEventListener('unhandledrejection', function(event) {
  console.error('Unhandled promise rejection:', event.reason);
  // Don't prevent default to allow normal error handling
});

// Global error handler for JavaScript errors
window.addEventListener('error', function(event) {
  console.error('Global JavaScript error:', event.error);
});