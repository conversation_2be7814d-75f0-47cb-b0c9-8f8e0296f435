import React from 'react';
import CustomFloatInput from '../-Helpers-/forms/CustomFloatInput';
import { Button, InputGroup, FormControl, Modal, Alert } from 'react-bootstrap';

const NewHelpers = (props) => {
  return (
    <>
      <CustomFloatInput
        name="password"
        updateValue={props.updateValue}
        value=""
        placeholder="סיסמה"
        cssClass=""
        validationRules={{ required: true, minlength: 3 }}
        typeInput="password"
      />
      <Button onClick={() => props.modalShow(false)} size="sm" variant="success">
        שליחה
      </Button>
    </>
  );
};

export default NewHelpers;