import 'react-app-polyfill/ie11';
import 'react-app-polyfill/stable';
import React from 'react';
import { createRoot } from 'react-dom/client';
import './env'; // Import environment variables
import './index.css';
import App from './App';
import ErrorBoundary from './Components/ErrorBoundary';
import debugHelper from './utils/debugHelper';

// Initialize debug helper
debugHelper.log('Application starting');

const root = createRoot(document.getElementById('root'));

// Add render timeout detection
const renderTimeout = setTimeout(() => {
  debugHelper.log('⚠️ Render timeout - app may be stuck');
  console.error('App render timeout - possible infinite loop or hang');
}, 15000);

try {
  root.render(
    <ErrorBoundary>
      <App />
    </ErrorBoundary>
  );

  // Clear timeout if render succeeds
  clearTimeout(renderTimeout);
  debugHelper.log('App rendered successfully');
} catch (error) {
  clearTimeout(renderTimeout);
  debugHelper.log('❌ App render failed', error);
  console.error('Failed to render app:', error);

  // Fallback UI
  document.getElementById('root').innerHTML = `
    <div style="
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      padding: 20px;
      text-align: center;
      font-family: Arial, sans-serif;
      direction: rtl;
    ">
      <h2 style="color: #e74c3c; margin-bottom: 20px;">שגיאה בטעינת האפליקציה</h2>
      <p style="margin-bottom: 20px;">אירעה שגיאה קריטית בטעינת האפליקציה</p>
      <button onclick="window.location.reload()" style="
        padding: 15px 30px;
        background: #3498db;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
      ">רענן דף</button>
    </div>
  `;
}
