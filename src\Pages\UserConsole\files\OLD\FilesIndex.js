import React, { useState } from 'react'
import { Accordion } from "react-bootstrap";
import { getSafeUserData } from "../../../../context/AuthContext";

import pInfo from "../../../img/sherut-leumi/svg/userData/pInfo.svg";
import { getAllUrlParams } from "./../../../Components/-Helpers-/UrlParameters";
import UploadFile from './UploadFile';
import loader from "../../../img/preLoader.gif";
import Spinner from 'react-bootstrap/Spinner';
import axios from 'axios';
import { RestUrls } from "../../../Components/-Helpers-/config";
import { toast } from 'react-toastify'
//import ShowDocuments from './ShowDocuments';

export default function FilesIndex(props) {

    const {userData} = props;
    //console.log(userData);

    const [loading, setLoading] = useState(false);
    const [showDocuments, setShowDocuments] = useState(false);
    const [responseData, setResponseData] = useState(false);

    const urlQuery = getAllUrlParams(window.location.href);

    let userJ = getSafeUserData();

    //console.log(userJ);

    if(showDocuments && !loading && !responseData) {

        const sendObj = {

            IDNumber: userJ.IDNO,
            SessionKey: userJ.SessionKey,
        }

        
        getFromApiSherutLeumi('/api/v2/volunteer/forms/list', sendObj, setLoading, setResponseData);
        //console.log('Loading');

    }

    //console.log(responseData);
    
    //http://localhost:3001/userConsole/data?files=1

    //NOT SHOW
    if(!urlQuery?.files) {
        return false;
    }

    return (<Accordion className="accordions acc3">
        <Accordion.Item eventKey="0">

          <Spinner animation="border" role="status">
        <span className="visually-hidden">Loading...</span>
      </Spinner>


            <Accordion.Header onClick={()=>setShowDocuments(true)}>
                <img src={pInfo} alt=">מסמכים" />
                <span>מסמכים</span>
            </Accordion.Header>

            <Accordion.Body>
                <div className='filesIndex'>
                    
                    <UploadFile userJ = {userJ} userData = {userData} />

                   {/*  <p className='pIndex'>כאן כמה מילים על המסמכים</p>
                    {responseData?.data?.Forms && <ShowDocuments forms = {responseData.data.Forms} />} */}
                    
                </div>
            </Accordion.Body>

        </Accordion.Item>
    </Accordion>)

  /* return (
    
  ) */
}

function getFromApiSherutLeumi(url,sendObj,setLoading, setResponseData) {


    setLoading(true);
    
    const json = JSON.stringify(  sendObj  );
    
    axios.post( RestUrls.sherutLeumiApi + url, json, {

        headers: {'Content-Type': 'application/json'},
        
    })
        .then( res => {

            //console.log(res);
            setResponseData(res);
        
        })
        
        .catch((error) => {
            toast.error('שגיאה');
            console.log(error)
        })
        
        .finally(() => {
            setLoading(false);
        })
            
    


}