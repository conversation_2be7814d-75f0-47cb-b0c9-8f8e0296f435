import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import debugHelper from '../utils/debugHelper';

const AuthContext = createContext();

// Helper to safely parse user data from localStorage
export const getSafeUserData = () => {
  try {
    const userDataStr = localStorage.getItem("userData");
    if (!userDataStr || userDataStr === "undefined" || userDataStr === "") {
      return null;
    }
    return JSON.parse(userDataStr);
  } catch (error) {
    console.error("Error parsing userData from localStorage:", error);
    return null;
  }
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  const API_BASE_URL = process.env.REACT_APP_ENVIRONMENT === "dev"
    ? process.env.REACT_APP_API_BASE_URL_DEV
    : process.env.REACT_APP_API_BASE_URL;

  debugHelper.log('AuthProvider initialized', { API_BASE_URL });



  // פונקציה לבדיקת תוקף הסשן מול השרת
  const checkSessionValidity = async (userData) => {
    if (!userData || !userData.SessionKey) {
      return false;
    }

    try {
      const response = await axios.post(
        `${API_BASE_URL}/api/test/checkAuthorization`,
        { sessionKey: userData.SessionKey },
        { timeout: 5000 } // הוספת timeout של 5 שניות
      );
      return response.status === 200;
    } catch (error) {
      console.error("Session validation error:", error);
      return false;
    }
  };

  // פונקציה להתנתקות
  const logout = (message = "התנתקת מהמערכת") => {
    localStorage.removeItem("userData");
    localStorage.removeItem("rakazid");
    localStorage.removeItem("sayeretid");
    setUser(null);
    navigate("/login");
    // הוספת הודעת toast
    if (message) {
      toast.success(message);
    }
  };

  // פונקציה להתחברות
  const login = (userData) => {
    try {
      localStorage.setItem("userData", JSON.stringify(userData));
      setUser(userData);
    } catch (error) {
      console.error("Error saving user data:", error);
      setError("שגיאה בשמירת נתוני המשתמש");
    }
  };

  // בדיקת אותנטיקציה בטעינת האפליקציה
  useEffect(() => {
    const initAuth = async () => {
      debugHelper.log('🔐 AuthContext: Starting initialization');
      console.log('🔐 AuthContext: Current URL:', window.location.href);

      // Skip auth initialization for public pages
      const currentPath = window.location.pathname;
      if (currentPath.startsWith('/sherutPlaces')) {
        debugHelper.log('🏢 Skipping auth initialization for public sherutPlaces page');
        setLoading(false);
        return;
      }

      setLoading(true);
      // Add timeout to prevent infinite loading
      const timeoutId = setTimeout(() => {
        debugHelper.log('⏰ Authentication timeout reached');
        console.warn("Authentication timeout reached");
        setLoading(false);
        setError("זמן טעינה חריג - נסו לרענן את הדף");
      }, 8000); // קיצור ל-8 שניות

      try {
        const userData = getSafeUserData();
        debugHelper.log('AuthContext: Checking userData', { hasUserData: !!userData });
        console.log("AuthContext: Initializing with userData:", userData ? "exists" : "null");

        if (userData) {
          console.log("AuthContext: Checking session validity...");
          try {
            const isSessionValid = await checkSessionValidity(userData);
            console.log("AuthContext: Session valid:", isSessionValid);

            if (isSessionValid) {
              setUser(userData);
              console.log("AuthContext: User set successfully");
            } else {
              console.log("AuthContext: Session invalid, clearing data");
              // ניקוי נתונים מקומיים במקום logout מיידי
              localStorage.removeItem("userData");
              localStorage.removeItem("rakazid");
              localStorage.removeItem("sayeretid");
              setUser(null);
            }
          } catch (sessionError) {
            console.error("Session check failed:", sessionError);
            // במקרה של שגיאה ברשת, נשאיר את המשתמש מחובר זמנית
            setUser(userData);
          }
        } else {
          console.log("AuthContext: No user data found");
        }
      } catch (error) {
        console.error("Authentication initialization error:", error);
        setError("שגיאה באימות המשתמש - " + error.message);
      } finally {
        clearTimeout(timeoutId);
        setLoading(false);
        console.log("AuthContext: Initialization completed");
      }
    };

    initAuth();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // בדיקה תקופתית של תוקף הסשן
  useEffect(() => {
    if (!user) return;

    const sessionCheckInterval = setInterval(async () => {
      try {
        const isSessionValid = await checkSessionValidity(user);
        if (!isSessionValid) {
          logout("פג תוקף החיבור שלך למערכת");
        }
      } catch (error) {
        console.error("Periodic session check failed:", error);
        // לא נעשה logout במקרה של שגיאת רשת
      }
    }, 10 * 60 * 1000); // בדיקה כל 10 דקות (פחות תכוף)

    return () => clearInterval(sessionCheckInterval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  return (
    <AuthContext.Provider value={{ user, loading, error, login, logout, checkSessionValidity }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
