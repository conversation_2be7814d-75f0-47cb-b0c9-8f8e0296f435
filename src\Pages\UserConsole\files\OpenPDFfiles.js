/**
 * OpenPDFfiles - קומפוננט לפתיחת והצגת קבצי PDF
 *
 * משמש בעיקר להצגת אישורים שונים כגון:
 * - אישור תקופת שירות
 * - דוח תקבולים
 * - טופס 101
 *
 * Props:
 * - data: נתוני המסמך מה-API
 */
import { Grid } from "@mui/material";
import React, { useState, useEffect } from "react";
import { Button, Modal } from "react-bootstrap";
import link from "../../../img/sherut-leumi/svg/files/link.svg";
import { filesTexts } from "./fileFunctions";
import logo from "../../../img/sherut-leumi/svg/logoPositive.svg";
import dayjs from "dayjs";
import { HebrewDate } from "hebrew-date";

// ייבוא של הממשק המאוחד לטיפול במסמכים ויצירת PDF
import { processDocumentContent, generateDocument } from "./pdfOperations";
import "./documentStyles.css";

// הוספת פונקציה לעיצוב טקסט לפסקאות
function formatPlainTextToParagraphs(html) {
  if (/<(p|table|ul|ol|li|h[1-6]|div|br)/i.test(html)) return html;
  return html
    .split(/\n|\r|(?<=\.) /)
    .map((line) => line.trim())
    .filter((line) => line.length > 0)
    .map((line) => `<p>${line}</p>`)
    .join("");
}

export default function OpenPDFfiles(props) {
  const { data } = props;

  const [showModal, setShowModal] = useState(false);
  const [formattedHtml, setFormattedHtml] = useState(data.Html);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  const textname = `link${data.TypeDocument}`;
  const texts = filesTexts[textname];

  // נשתמש בחתימות מהתיקייה הציבורית:
  const DEFAULT_SIGNATURES = {
    logo: logo, // השאר את הלוגו המקורי שכבר נטען
    yaron: process.env.PUBLIC_URL + "/yaron.jpg", // חתימה של ירון בתיקיית public
    aguda: process.env.PUBLIC_URL + "/aguda_sig.jpg", // חתימת האגודה בתיקיית public
  };

  useEffect(() => {
    if (!data.Html) return;

    // עיבוד המסמך באמצעות הממשק המאוחד עם התבנית האחידה החדשה
    const processedHtml = processDocumentContent(
      data.Html,
      data.TypeDocument,
      logo
    );
    setFormattedHtml(processedHtml);
  }, [data.Html, data.TypeDocument]);

  const downloadDocument = () => {
    setIsGeneratingPDF(true);

    // יצירת PDF באמצעות הממשק המאוחד
    generateDocument(
      formattedHtml,
      data.TypeDocument,
      `${texts?.title || "document"}.pdf`,
      logo,
      DEFAULT_SIGNATURES,
      () => setIsGeneratingPDF(false),
      (error) => {
        console.error("שגיאה ביצירת PDF:", error);
        setIsGeneratingPDF(false);
      }
    );
  };

  const getHebrewDate = (dateStr) => {
    const date = dayjs(dateStr);
    const hebrewDate = new HebrewDate(date.toDate());
    return hebrewDate.toString();
  };

  return (
    <>
      <div className="w-full md:w-1/3 lg:w-1/4 p-2">
        <div className="bg-white rounded-lg shadow-md p-4 h-full">
          <div className="header mb-4">
            <h3 className="flex items-center text-lg font-semibold text-gray-700">
              <img src={link} alt="link" className="w-6 h-6 mr-2" />
              <span>{texts.title}</span>
            </h3>
          </div>

          <div className="grid grid-cols-1 gap-3 mt-4">
            <Button
              className="flex items-center w-full justify-center gap-2 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200 shadow-sm"
              onClick={() => setShowModal(true)}
            >
              לצפייה
            </Button>
            <Button
              className="flex items-center w-full justify-center gap-2 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors duration-200 shadow-sm"
              onClick={downloadDocument}
              disabled={isGeneratingPDF}
            >
              {isGeneratingPDF ? "מכין הורדה..." : "הורדה כ-PDF"}
            </Button>
          </div>
        </div>
      </div>

      <Modal
        className="animate__animated animate__fadeInDown animate__faster modalSite filesModal modalHtml"
        animation={false}
        onHide={() => setShowModal(false)}
        size="lg"
        show={showModal}
        dialogClassName="modal-90w"
        aria-labelledby="document-modal"
      >
        <Modal.Body>
          <Button
            className="closeBtn"
            onClick={() => setShowModal(false)}
            variant="secondary"
            style={{
              position: "fixed",
              top: "20px",
              right: "20px",
              zIndex: 1050,
              borderRadius: "50%",
              width: "40px",
              height: "40px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
              border: "none",
              background: "#fff",
              color: "#333",
            }}
          >
            X
          </Button>

          <div
            className="document-viewer showHtml mb-4"
            dangerouslySetInnerHTML={{
              __html: formatPlainTextToParagraphs(formattedHtml),
            }}
          />

          <Grid
            className="btnCont"
            container
            spacing={2}
            alignItems="center"
            direction="row"
            justifyContent="center"
          >
            <Grid item md={2} xs={6}>
              <Button
                variant="secondary"
                className="w-full p-2 mb-2"
                onClick={() => setShowModal(false)}
              >
                סגירה
              </Button>
            </Grid>

            <Grid item md={2} xs={6}>
              <Button
                variant="primary"
                className="w-full p-2 mb-2"
                onClick={downloadDocument}
                disabled={isGeneratingPDF}
              >
                {isGeneratingPDF ? "מכין..." : "הורדה כ-PDF"}
              </Button>
            </Grid>
          </Grid>
        </Modal.Body>
      </Modal>
    </>
  );
}
