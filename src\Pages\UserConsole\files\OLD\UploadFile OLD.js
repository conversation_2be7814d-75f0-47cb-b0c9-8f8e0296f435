import React,{useState} from 'react'

import axios from 'axios';
import { useDropzone } from 'react-dropzone' /* https://react-dropzone.js.org/ */
import { toast } from 'react-toastify'

import { RestUrls } from "../../../Components/-Helpers-/config";

import NoPic from './../../../img/sherut-leumi/pic.png';

import { Row, Col, ProgressBar } from 'react-bootstrap';

export default function UploadFile(props) {

    const { userJ } = props

    const [picUrl, setPicUrl] = useState(false);
    const [isPicture, setIsPicture] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(null);
    const [uploaded, setUploaded] = useState(false);

    
    const item = {

        name : 'שם הטופס',
        formId : 1

    }

    const onDrop = (acceptedFiles) => {
        
        let file = acceptedFiles[0];
        let filename = file?.name;

        //setFilenameState(filename);
        //console.log(file);
        //const text = file?.type + ' | ' + file?.size + ' | ';
        //setFilenameState2 (text);

        setPicUrl(URL.createObjectURL(file));

        if( !filename.includes('.pdf') && (filename.includes('.jpg') || filename.includes('.jpeg') || filename.includes('.png') ) ) {
            setIsPicture(true);
        } else {
            //console.log(filename);
            setIsPicture(false);
        }
        
        uploadImage(file);
        
    }

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        //accept: 'pdf,image/jpeg, image/png',
        noKeyboard: true,
        //maxFiles:1,
        //maxSize:4096,
        multiple: false,
        onDrop,
    })


    const uploadImage = (file) => {
        //console.log(file)
     
        fileUploadHandler(file);
        
     }
 
 
 
    const  fileUploadHandler = (file) => {

        // let userJ = JSON.parse(localStorage.getItem('user-info'));
        // let user = userJ.data;

        console.log(file);
        return true;

        const fd = new FormData();

        fd.append('IDNumber', userJ.IDNO);
        fd.append('SessionKey', userJ.SessionKey);
        fd.append('formId', formId);
        fd.append('myImage', file, file.name);
        //fd.append('myFile', file, file.name);

        console.log(fd);


        const json = JSON.stringify(
            { 
                IDNumber: userJ.IDNO,
                SessionKey: userJ.SessionKey,
                formId: formId
            }
        );


        //axios.post( RestUrls.baseApiUrl + '/documents/getDocummentPost', fd, {
        //axios.post( RestUrls.baseApiUrl + '/documents/getDocummentPost', json, {
        axios.post( RestUrls.baseApiUrl + '/documents/getDocummentPost', json, {

            headers: {

                'Content-Type': 'application/json'

            },

            onUploadProgress: progressEvent => {

                setUploadProgress(Math.round(progressEvent.loaded / progressEvent.total * 100 ));

            }
        })
            .then( res => {

                console.log(res);
                //console.log(res.data);

                /* if(res.data?.ok) {

                    toast.success(res.data.ok);
                    setUploaded(true);

                } else {

                    if(res.data?.error) {

                        setPicUrl(false);
                        setIsPicture(false);
                        toast.error(res.data.error);
                        
                        //const errors = JSON.stringify(res.data.errors);
                        //toast.error(errors);
                        

                    } else {

                        toast.error('שגיאה');

                    }
                    
                } */

            
            })
            
            .catch((error) => {
                console.log(error)
            })
            
            .finally(() => {
                setUploadProgress(null);
            })
            
    }



  return (<>
        <Row className='fileCont'>
            
            <Col  md="7" xs="7" className='catName'>
                <h3>{item.name}</h3>
            </Col>
            
            <Col  md="5" xs="5" className='imgCol' >
                <div className="user-file" {...getRootProps()}>
                

                <input {...getInputProps()} />
                {isDragActive ? (
                    <img className='drag' src={NoPic} alt={item.name} />
                ) : (
                    <img className={`noDrag ${!picUrl && 'noPic'}`} src={ picUrl && isPicture ? picUrl : NoPic } alt={item.name}/>
                )}
                {uploaded && <p className='animate__animated animate__bounce'>לחצו לקובץ נוסף</p>}
                </div>
            </Col>
        </Row>

        {uploadProgress && <ProgressBar now={uploadProgress} /> }
        
        
        </>)
}
